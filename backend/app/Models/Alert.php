<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Alert extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'monitoring_task_id',
        'alert_rule_id',
        'user_id',
        'product_id',
        'sku_id',
        'title',
        'message',
        'severity',
        'priority',
        'trigger_data',
        'context_data',
        'trigger_field',
        'trigger_value',
        'threshold_value',
        'operator',
        'product_title',
        'status',
        'resolved_at',
        'resolved_by',
        'resolution_note',
        'notification_status',
        'notification_attempts',
        'last_notification_at',
        'view_count',
        'first_viewed_at',
        'last_viewed_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'trigger_data' => 'array',
        'status' => 'integer',
        'resolved_at' => 'datetime',
    ];

    /**
     * 所属监控任务
     */
    public function monitoringTask(): BelongsTo
    {
        return $this->belongsTo(MonitoringTask::class);
    }

    /**
     * 预警规则
     */
    public function alertRule(): BelongsTo
    {
        return $this->belongsTo(AlertRule::class);
    }

    /**
     * 处理人
     */
    public function handler(): BelongsTo
    {
        return $this->belongsTo(User::class, 'resolved_by');
    }
} 